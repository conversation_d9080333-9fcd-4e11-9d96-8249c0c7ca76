jQuery(document).ready(function($) {
    
    $('#load_lists').on('click', function() {
        var button = $(this);
        var select = $('#list_id');
        var selectedValue = select.data('selected') || select.val();

        button.prop('disabled', true).text(ddGrCf7Ajax.strings.loading);

        $.ajax({
            url: ddGrCf7Ajax.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dd_gr_cf7_get_lists',
                nonce: ddGrCf7Ajax.nonce
            },
            success: function(response) {
                if (response.success && response.lists) {
                    select.empty().append('<option value="">Wybierz listę...</option>');

                    $.each(response.lists, function(index, list) {
                        var option = $('<option></option>')
                            .attr('value', list.id)
                            .text(list.name);

                        if (list.id === selectedValue) {
                            option.prop('selected', true);
                        }

                        select.append(option);
                    });

                    if (response.lists.length === 0) {
                        select.append('<option value="">Brak dostępnych list</option>');
                    }
                } else {
                    alert(response.error || ddGrCf7Ajax.strings.error);
                }
            },
            error: function() {
                alert(ddGrCf7Ajax.strings.error);
            },
            complete: function() {
                button.prop('disabled', false).text('Załaduj listy');
            }
        });
    });
    
    $('.delete-tag').on('click', function() {
        var button = $(this);
        var tagId = button.data('tag-id');
        var row = button.closest('tr');
        
        if (!confirm('Czy na pewno chcesz usunąć ten tag?')) {
            return;
        }
        
        button.prop('disabled', true);
        row.addClass('loading');
        
        $.ajax({
            url: ddGrCf7Ajax.ajaxUrl,
            type: 'POST',
            data: {
                action: 'dd_gr_cf7_delete_tag',
                nonce: ddGrCf7Ajax.nonce,
                tag_id: tagId
            },
            success: function(response) {
                if (response.success) {
                    row.fadeOut(function() {
                        row.remove();
                        
                        if ($('.tags-list-section tbody tr').length === 0) {
                            $('.tags-list-section .wp-list-table').replaceWith(
                                '<p>Brak tagów. Dodaj pierwszy tag powyżej.</p>'
                            );
                        }
                    });
                } else {
                    alert(response.data || ddGrCf7Ajax.strings.error);
                    button.prop('disabled', false);
                    row.removeClass('loading');
                }
            },
            error: function() {
                alert(ddGrCf7Ajax.strings.error);
                button.prop('disabled', false);
                row.removeClass('loading');
            }
        });
    });
    
    var editTagId = $('input[name="tag_id"]').val();
    if (editTagId) {
        $('#load_lists').trigger('click');
    }
    
});
